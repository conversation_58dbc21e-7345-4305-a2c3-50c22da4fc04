<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hamburger Menu Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #000;
            color: #fff;
            height: 200vh;
        }
        
        .hamberger-menu {
            cursor: pointer;
            padding: 10px;
            background: #333;
            color: #fff;
            border: none;
            border-radius: 4px;
            display: inline-block;
            margin: 20px;
        }
        
        .overlay {
            height: 0%;
            width: 100%;
            position: fixed;
            z-index: 9999;
            top: 0;
            left: 0;
            background-color: rgba(0, 0, 0, 0.9);
            overflow-y: hidden;
            transition: height 0.5s ease;
        }
        
        .overlay-content {
            position: relative;
            top: 25%;
            width: 100%;
            text-align: center;
            margin-top: 30px;
        }
        
        .overlay a {
            padding: 8px;
            text-decoration: none;
            font-size: 36px;
            color: #818181;
            display: block;
            transition: 0.3s;
        }
        
        .closebtn {
            position: absolute;
            top: 20px;
            right: 45px;
            font-size: 60px;
            cursor: pointer;
            color: #fff;
        }
    </style>
</head>
<body>
    <h1>Hamburger Menu Debug Test</h1>
    
    <p>Click the hamburger menu below to test functionality:</p>
    
    <span class="hamberger-menu" onclick="console.log('Direct onclick triggered'); openNav();">
        ☰ Test Hamburger Menu
    </span>
    
    <div id="myNav" class="overlay">
        <a href="javascript:void(0)" class="closebtn" onclick="console.log('Close button clicked'); closeNav();">&times;</a>
        <div class="overlay-content">
            <a href="#">Home</a>
            <a href="#">About</a>
            <a href="#">Services</a>
            <a href="#">Contact</a>
        </div>
    </div>
    
    <p>Scroll down to test scroll-lock behavior...</p>
    
    <div style="height: 100vh; background: linear-gradient(45deg, #333, #666); margin: 20px 0; padding: 20px;">
        <h2>Scrollable Content</h2>
        <p>This content should not be scrollable when the menu is open.</p>
    </div>

    <script>
        console.log('Debug script loading...');
        
        // Simple test functions
        function openNav() {
            console.log('openNav() called directly');
            const overlay = document.getElementById("myNav");
            console.log('Overlay element found:', !!overlay);
            
            if (overlay) {
                console.log('Setting overlay height to 100%');
                overlay.style.height = "100%";
                
                // Disable scrolling
                document.body.style.overflow = 'hidden';
                document.documentElement.style.overflow = 'hidden';
                console.log('Scrolling disabled');
            } else {
                console.error('Overlay element not found!');
            }
        }

        function closeNav() {
            console.log('closeNav() called directly');
            const overlay = document.getElementById("myNav");
            console.log('Overlay element found:', !!overlay);
            
            if (overlay) {
                console.log('Setting overlay height to 0%');
                overlay.style.height = "0%";
                
                // Enable scrolling
                document.body.style.overflow = '';
                document.documentElement.style.overflow = '';
                console.log('Scrolling enabled');
            } else {
                console.error('Overlay element not found!');
            }
        }
        
        // Test element detection
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded - testing element detection');
            
            const overlay = document.getElementById("myNav");
            const hamburger = document.querySelector(".hamberger-menu");
            const closeBtn = document.querySelector(".closebtn");
            
            console.log('Elements found:', {
                overlay: !!overlay,
                hamburger: !!hamburger,
                closeBtn: !!closeBtn
            });
            
            // Test direct click event
            if (hamburger) {
                hamburger.addEventListener('click', function(e) {
                    console.log('Event listener click detected');
                    e.preventDefault();
                });
            }
        });
        
        console.log('Debug script loaded successfully');
    </script>
</body>
</html>

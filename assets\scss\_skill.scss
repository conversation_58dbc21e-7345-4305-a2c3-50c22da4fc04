/*======= Skill Section =======*/
.skill-section {
	position: relative;
	padding-bottom: 80px;

	&::before {
		position: absolute;
		left: 0;
		top: 0;
		content: '';
		height: 940px;
		width: 940px;
		border-radius: 50%;
		background-color: $soft-gery;
		z-index: -1;
		transform: translate(-285px, -50%);

		@media #{$md} {
			height: 600px;
			width: 600px;
		}

		@media #{$sm} {
			height: 500px;
			width: 500px;
		}
	}

	.skill-text {
		padding-right: 55px;

		@media #{$lg} {
			padding-right: 0;
		}

		@media #{$md} {
			margin-bottom: 50px;
		}

		p {
			margin-bottom: 20px;
		}

		.main-btn {
			margin-top: 10px;
		}
	}

	.piechart-boxes {
		display: grid;
		grid-template-columns: repeat(2, 1fr);

		@media #{$tiny} {
			grid-template-columns: repeat(1, 1fr);
		}

		.chart-box {
			text-align: center;
			margin-bottom: 50px;

			.chart {
				position: relative;
				max-width: 220px;
				margin: auto;

				@media #{$lg} {
					max-width: 200px;
				}

				@media #{$sm} {
					max-width: 150px;
				}

				canvas {
					@media #{$lg} {
						width: 200px;
						height: 200px;
					}

					@media #{$sm} {
						width: 150px;
						height: 150px;
					}
				}


				i {
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
					font-size: 50px;
					color: $secondary-color;

					@media #{$sm} {
						font-size: 40px;
					}
				}
			}

			.title {
				font-size: 24px;
				font-weight: 600;
				margin-top: 25px;

				@media #{$xsm} {
					font-size: 18px;
				}
			}
		}
	}

	&.skill-section-two {
		padding: 130px 0;

		&::before {
			display: none;
		}
	}

	.skill-progress-bars {
		.skill-progress {
			margin-bottom: 30px;

			&:last-child {
				margin-bottom: 0;
			}

			.title {
				font-size: 20px;
				font-family: $ssp;
				font-weight: 600;
				margin-bottom: 15px;
				color: $secondary-color;
			}

			.progressbar-wrap {
				width: 100%;
				height: 5px;
				background-color: #e7e8e9;
				position: relative;

				.progressbar {
					position: absolute;
					left: 0;
					top: 0;
					width: 0;
					height: 100%;
					background-color: $primary-color;
					transition: width 2s ease-in-out;
				}
			}
		}
	}
}
/*======= Blog Details  =======*/
.post-details-wrap {
    .post-thumb {
        margin-bottom: 40px;
    }

    .post-meta {
        margin-bottom: 15px;

        ul li {
            display: inline-block;
            margin-right: 30px;
            font-weight: 600;

            @media #{$sm} {
                margin-right: 15px;
            }

            &,
            a {
                color: $text-color;

                &:hover {
                    color: $primary-color;
                }
            }

            i {
                color: $primary-color;
                margin-right: 10px;
            }
        }
    }

    .post-content {
        font-size: 15px;

        .title {
            font-size: 35px;
            font-weight: 600;
            margin-bottom: 30px;

            @media #{$lg} {
                font-size: 28px;
            }

            @media #{$sm} {
                font-size: 24px;
            }

            @media #{$xsm} {
                font-size: 20px;
            }
        }

        blockquote {
            padding: 50px 40px;
            background-color: $secondary-color;
            position: relative;
            z-index: 1;
            font-size: 35px;
            line-height: 1.2;
            font-family: $ssp;
            font-weight: 600;
            color: $white;
            margin: 40px 0;

            @media #{$lg} {
                font-size: 28px;
            }

            @media #{$sm} {
                font-size: 24px;
            }

            @media #{$xsm} {
                padding: 40px 30px;
                font-size: 20px;
            }

            &::before {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                z-index: -1;
                content: "";
                background-image: url(../img/icons/quote-line.png);
                opacity: 0.06;
                background-size: auto;
                background-position: 38% 50%;
                background-repeat: no-repeat;

            }

            .author {
                font-size: 18px;
                position: relative;
                display: block;
                padding-left: 70px;
                margin-top: 25px;
                line-height: 1;

                @media #{$xsm} {
                    padding-left: 50px;
                }

                &::before {
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    height: 3px;
                    width: 40px;
                    background-color: $white;
                    content: "";

                    @media #{$xsm} {
                        width: 30px;
                    }
                }
            }
        }

        h4.with-check {
            margin-bottom: 20px;
            font-size: 24px;

            @media #{$lg} {
                font-size: 22px;
            }

            @media #{$xsm} {
                font-size: 20px;
            }

            i {
                margin-right: 15px;

                @media #{$lg} {
                    margin-right: 10px;
                }
            }
        }
    }

    .post-footer {
        margin-top: 40px;

        ul li {
            display: inline-block;

            a {
                color: $text-color;

                &:hover {
                    color: $primary-color;
                }
            }

            &.title {
                font-weight: 700;
                color: $secondary-color;
                font-family: $ssp;
            }
        }

        .post-share {
            a {
                padding: 5px;
                margin-left: 5px;

                i {
                    font-size: 15px;
                }
            }
        }
    }
}

.post-author-info {
    background-color: $soft-gery;
    padding: 40px;
    display: grid;
    grid-template-columns: 230px 1fr;
    grid-column-gap: 25px;
    align-items: center;
    margin-top: 80px;

    @media #{$lg} {
        padding: 30px;
    }

    @media #{$sm} {
        display: block;
    }

    .author-desc {
        font-size: 15px;

        @media #{$sm} {
            margin-top: 30px;
        }

        h4 {
            font-size: 24px;
            margin-bottom: 20px;

            @media #{$lg} {
                font-size: 22px;
                margin-bottom: 15px;
            }
        }

        ul.social-links {
            margin-top: 15px;

            @media #{$lg} {
                margin-top: 10px;
            }

            li {
                display: inline-block;
                margin-right: 15px;

                a {
                    color: $text-color;

                    &:hover {
                        color: $primary-color;
                    }
                }
            }
        }
    }
}

.post-nav {
    margin: 80px 0;

    .prev-post,
    .next-post {
        display: grid;
        grid-template-columns: 75px 1fr;
        grid-column-gap: 15px;
        align-items: center;

        .date {
            font-size: 15px;

            i {
                margin-right: 10px;
                color: $primary-color;
            }
        }
    }

    .next-post {
        grid-template-columns: 1fr 75px;
        text-align: right;

        @media #{$sm} {
            text-align: left;
            grid-template-columns: 75px 1fr;
            margin-top: 30px;

            .content {
                order: 2;
            }

            .img {
                order: 1;
            }
        }
    }
}

.related-post {
    margin-bottom: 80px;

    .title {
        font-size: 35px;
        font-weight: 600;
        margin-bottom: 40px;

        @media #{$lg} {
            font-size: 28px;
        }

        @media #{$sm} {
            font-size: 24px;
        }

        @media #{$xsm} {
            font-size: 20px;
        }
    }
}

/*======= Comment Template  =======*/
.comment-template {
    .title {
        font-size: 35px;
        font-weight: 600;
        margin-bottom: 40px;

        @media #{$lg} {
            font-size: 28px;
        }

        @media #{$sm} {
            font-size: 24px;
        }

        @media #{$xsm} {
            font-size: 20px;
        }
    }

    .comment-list {
        li {
            margin-bottom: 45px;
            position: relative;
            padding-left: 130px;

            @media #{$xsm} {
                padding-left: 0;
                padding-top: 130px;
            }

            .author-img {
                position: absolute;
                left: 0;
                top: 10px;

                @media #{$xsm} {
                    top: 0;
                }
            }

            .comment-content {
                font-size: 15px;

                .author-name {
                    font-size: 18px;
                    margin-bottom: 10px;

                    .date {
                        font-size: 13px;
                        color: $text-color;
                        font-weight: 400;
                        font-family: $nuns;
                        margin-left: 15px;
                    }
                }

                .reply-btn {
                    margin-top: 5px;
                    color: $text-color;
                    font-weight: 700;
                    font-size: 18px;

                    i {
                        margin-left: 8px;
                        position: relative;
                        top: 2px;
                    }

                    &:hover {
                        color: $primary-color;
                    }
                }

            }

            .children {
                li {
                    margin-bottom: 0;
                    margin-top: 45px;
                    margin-left: -75px;

                    @media #{$xsm} {
                        margin-left: 50px;
                    }
                }
            }

            &:last-child {
                margin-bottom: 0;
            }
        }
    }

    .comment-form {
        margin-top: 80px;
        padding: 50px;
        background-color: $soft-gery;

        @media #{$lg} {
            padding: 35px;
        }
    }
}

/*=======  Services Details =======*/
.service-details {
    .service-details-content {
        font-size: 15px;

        .title {
            font-size: 45px;
            font-weight: 600;
            margin-bottom: 25px;

            @media #{$lg} {
                font-size: 35px;

            }
        }

        .circle-check-list {
            li {
                position: relative;
                padding-left: 70px;

                &:not(&:last-child) {
                    margin-bottom: 30px;
                }

                @media #{$xsm} {
                    padding-left: 60px;
                }

                i {
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    border: 2px solid $primary-color;
                    color: $primary-color;
                    width: 50px;
                    height: 50px;
                    border-radius: 50%;
                    line-height: 48px;
                    text-align: center;

                    @media #{$xsm} {
                        height: 40px;
                        width: 40px;
                        line-height: 38px;
                        font-size: 14px;
                    }
                }
            }
        }

        .sub-title {
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 25px;

            @media #{$lg} {
                font-size: 28px;
                margin-bottom: 20px;
            }
        }

        .check-list {
            li {
                font-weight: 600;
                color: $secondary-color;

                &:not(:last-child) {
                    margin-bottom: 5px;
                }

                i {
                    color: $primary-color;
                    margin-right: 15px;
                }
            }
        }
    }
}

/*=======  Project Details =======*/
.project-details {
    .project-content {
        .content {
            h2 {
                font-size: 55px;
                margin-bottom: 25px;

                @media #{$sm} {
                    font-size: 40px;
                }

                @media #{$xsm} {
                    font-size: 30px;
                    margin-bottom: 15px;
                }

                @media #{$tiny} {
                    font-size: 25px;
                }
            }

            p {
                font-size: 15px;
                line-height: 2;
            }
        }

        .details {
            background-color: $secondary-color;
            padding: 60px 45px;

            @media #{$lg} {
                padding: 60px 40px;
            }

            @media #{$md} {
                text-align: center;
                padding: 30px 0;
                margin-bottom: 30px;
            }

            @media #{$xsm} {
                text-align: left;
                padding: 50px 30px;
                margin-bottom: 30px;
            }

            ul {
                li {
                    color: #c7dff2;
                    font-size: 15px;

                    @media #{$md} {
                        display: inline-block;
                    }

                    @media #{$xsm} {
                        display: block;
                    }

                    h3 {
                        color: $white;
                        font-size: 35px;
                        margin-bottom: 15px;

                        @media #{$md} {
                            font-size: 30px;
                        }

                        @media #{$sm} {
                            font-size: 22px;
                            margin-bottom: 10px;
                        }
                    }

                    &:not(:last-child) {
                        padding-bottom: 30px;
                        margin-bottom: 30px;
                        border-bottom: 1px solid #334959;

                        @media #{$md} {
                            padding: 0 15px 0 0;
                            margin: 0 15px 0 0;
                            border-bottom: none;
                            border-right: 1px solid #334959;
                        }

                        @media #{$xsm} {
                            padding: 0 0 15px 0;
                            margin: 0 0 15px 0;
                            border-bottom: 1px solid #334959;
                            border-right: none;
                        }
                    }
                }
            }
        }

        .thumbs {
            margin-top: 70px;

            img {
                margin-bottom: 40px;
            }
        }
    }
}

/*======= Team Members Details=======*/
.member-details-wrapper {
    .member-details {
        display: grid;
        grid-template-columns: 270px 1fr 1fr;
        grid-gap: 60px;

        @media #{$lg} {
            align-items: flex-start;
            grid-gap: 30px;
        }

        @media #{$md} {
            grid-gap: 30px;
            grid-template-columns: 1fr 1fr;
        }

        @media #{$sm} {
            grid-template-columns: 1fr;
        }

        .member-picture-wrap {
            text-align: center;


            @media #{$md} {
                grid-column: 1/-1;
                margin-bottom: 20px;
            }

            .member-picture {
                position: relative;
                z-index: 1;
                display: inline-block;

                &::after {
                    position: absolute;
                    content: '';
                    right: -10px;
                    bottom: -10px;
                    width: 80px;
                    height: 70px;
                    background-color: $primary-color;
                    z-index: -1;
                    clip-path: polygon(100% 0, 0 100%, 100% 100%);
                }
            }
        }

        .member-desc,
        .member-contact-info {
            padding: 35px;
            border: 1px solid #e9e9e9;
            font-size: 15px;

            @media #{$lg} {
                padding: 25px;
            }

            .title,
            .name {
                font-size: 35px;
                margin-bottom: 5px;

                @media #{$lg} {
                    font-size: 28px;
                }
            }

            span.title-tag,
            span.pro {
                color: $primary-color;
                font-weight: 600;
                margin-bottom: 20px;
                line-height: 1;
            }
        }

        .member-desc {

            p:not(:last-child) {
                margin-bottom: 15px;
            }
        }

        .member-contact-info {
            .social-links {
                margin-top: 20px;

                li {
                    display: inline-block;
                    margin-right: 10px;

                    a {
                        color: $text-color;

                        &:hover {
                            color: $primary-color;
                        }
                    }
                }
            }

            .contact-info {
                li {
                    &:not(:last-child) {
                        margin-bottom: 10px;
                    }

                    i {
                        margin-right: 10px;
                        color: $primary-color;
                    }

                    &,
                    a {
                        color: $text-color;
                    }
                }
            }
        }
    }
}
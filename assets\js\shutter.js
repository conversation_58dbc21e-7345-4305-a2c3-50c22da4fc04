// Blog Horizontal Scroll with GSAP
document.addEventListener("DOMContentLoaded", function () {
  // Wait for GSAP to be fully loaded
  setTimeout(() => {
    try {
      console.log("🔍 Setting up blog horizontal scroll...");

      // Check if GSAP and ScrollTrigger are available
      if (typeof gsap === "undefined") {
        console.error("❌ GSAP is not loaded!");
        return;
      }
      if (typeof ScrollTrigger === "undefined") {
        console.error("❌ ScrollTrigger is not loaded!");
        return;
      }

      const blogSection = document.querySelector(
        ".blog-carousel-section"
      );
      const wrapper = document.getElementById("blog-cards-wrapper");

      if (blogSection && wrapper) {
        console.log(`✅ Found blog section and wrapper`);

        // Calculate total scroll distance
        const getScrollDistance = () => {
          const wrapperWidth = wrapper.scrollWidth;
          const viewportWidth = window.innerWidth;

          // Use actual scrollWidth but add extra buffer to ensure all cards are visible
          const extraBuffer = 200; // Extra pixels to ensure last card is fully visible
          const distance = wrapperWidth - viewportWidth + extraBuffer;

          console.log(`📐 Enhanced scroll calculation:
            - Wrapper scrollWidth: ${wrapperWidth}px
            - Viewport width: ${viewportWidth}px
            - Extra buffer: ${extraBuffer}px
            - Total scroll distance: ${distance}px`);

          return Math.max(distance, 0);
        };

        // Create horizontal scroll animation with optimized settings
        const totalScrollLength = getScrollDistance();

        gsap.to(wrapper, {
          x: () => `-${totalScrollLength}px`,
          ease: "none",
          scrollTrigger: {
            trigger: blogSection,
            start: "top top",
            end: () => `+=${blogSection.offsetHeight}`, // Use full section height for complete scroll
            scrub: 1,
            pin: true,
            anticipatePin: 1,
            invalidateOnRefresh: true,
            pinSpacing: false, // Prevents extra spacing after animation
            onStart: () => {
              console.log("🚀 Blog horizontal scroll started");
            },
            onUpdate: (self) => {
              const progress = Math.round(self.progress * 100);
              if (progress % 20 === 0) {
                console.log(`📊 Scroll progress: ${progress}%`);
              }
            },
            onComplete: () => {
              console.log("✅ Blog horizontal scroll completed");
            },
            onLeave: () => {
              // Ensure smooth transition to footer
              console.log(
                "🏁 Leaving blog section, transitioning to footer"
              );
            },
          },
        });

        // Add entrance animation for cards
        gsap.fromTo(
          ".blog-card",
          {
            opacity: 0,
            y: 50,
          },
          {
            opacity: 1,
            y: 0,
            duration: 0.8,
            stagger: 0.1,
            ease: "power2.out",
            scrollTrigger: {
              trigger: blogSection,
              start: "top 80%",
              toggleActions: "play none none reverse",
            },
          }
        );

        // Handle resize with debouncing
        let resizeTimer;
        window.addEventListener("resize", () => {
          clearTimeout(resizeTimer);
          resizeTimer = setTimeout(() => {
            console.log("🔄 Refreshing on resize");
            ScrollTrigger.refresh();
          }, 300);
        });

        console.log("✅ Blog horizontal scroll setup complete");
      } else {
        console.error("❌ Blog section elements not found:", {
          blogSection: !!blogSection,
          wrapper: !!wrapper,
        });
      }
    } catch (error) {
      console.error("❌ Error in blog horizontal scroll setup:", error);
    }
  }, 1000); // Wait 1 second for all animations to be ready
});

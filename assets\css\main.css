
/* Popup Animation Keyframes */
@keyframes popupSlideUp {
  0% {
    opacity: 0;
    transform: translateY(var(--start-y, 30px)) scale(var(--start-scale, 0.9));
  }
  60% {
    opacity: 0.8;
    transform: translateY(-5px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Mobile Image Slide Animations */
@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-100px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(100px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Initial state for left and right images - hidden until animated */
.left, .right {
  opacity: 0;
  transform: translateX(-100px);
}

.right {
  transform: translateX(100px);
}

/* Animation classes to be added when in viewport */
.left.animate-in {
  animation: slideInLeft 1s ease-out forwards;
}

.right.animate-in {
  animation: slideInRight 1s ease-out forwards;
}

/* Our Work Content Transition Styles */
.work-transition-section {
  height: 100vh; /* Single viewport height for desktop */
  background-color: #000;
  position: relative;
  overflow: hidden;
}

.work-sticky-container {
  position: sticky;
  top: 0;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #000;
}

.work-section-title {
  position: absolute;
  top: 5%;
  /* left: 2rem; */
  z-index: 10;
  width: 100%;
  pointer-events: none;
}

.work-section-title .title-big h2 {
  color: #fff;
  font-size: 4rem;
  text-align: left;
  margin-bottom: 0;
}

.work-title-line {
  width: 120px;
  height: 4px;
  background: linear-gradient(90deg, #fff 0%, #888 100%);
  margin-top: 1rem;
  border-radius: 2px;
}

.work-content-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  padding-top: 20%;
  align-items: center;
  justify-content: center;
}

.work-project {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 1;
  will-change: opacity;
}

.work-content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  max-width: 1200px;
  width: 100%;
  padding: 0 2rem;
}

.work-content-left {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.project-title {
  font-size: 4rem;
  color: #fff;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.project-credits {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.project-credits li {
  color: #fff;
  font-size: 1.1rem;
}

.project-credits .text-grey {
  color: #888;
  margin-right: 0.5rem;
}

.project-btn-wrap {
  margin-top: 1rem;
}

.project-btn {
  display: inline-flex;
  align-items: center;
  padding: 1rem 2rem;
  background-color: transparent;
  border: 2px solid #fff;
  color: #fff;
  text-decoration: none;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border-radius: 50px;
}

.project-btn:hover {
  background-color: #fff;
  color: #000;
  transform: translateY(-2px);
}

.work-content-right {
  display: flex;
  justify-content: center;
  align-items: center;
}

.project-image-container {
  position: relative;
  width: 100%;
  max-width: 500px;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.project-image-container:hover {
  transform: scale(1.05);
}

.project-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.3s ease;
}

.view-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.9);
  color: #000;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  font-weight: 700;
  font-size: 0.9rem;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.project-image-container:hover .view-overlay {
  opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .work-content-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
  
  .project-title {
    font-size: 2.5rem;
  }
  
  .work-section-title .title-big h2 {
    font-size: 2.5rem;
  }
  
  .work-section-title {
    top: 5%;
    left: 1rem;
  }
  
  .work-title-line {
    width: 80px;
    height: 3px;
  }
  
  .work-transition-section {
    height: 150vh;
  }
}

@media (max-width: 500px) and (min-width: 300px) {
  /* Remove scroll-based animations for mobile */
  .work-transition-section {
    height: auto !important; /* Remove fixed height */
    position: relative !important;
    overflow: visible !important;
  }
  
  .work-sticky-container {
    position: relative !important; /* Remove sticky positioning */
    height: auto !important;
    display: block !important;
    overflow: visible !important;
  }
  
  .work-content-container {
    position: relative !important;
    height: auto !important;
    display: block !important;
    padding: 2rem 0 !important;
  }
  
  /* Arrange projects vertically */
  .work-project {
    position: relative !important; /* Remove absolute positioning */
    opacity: 1 !important; /* Always visible */
    display: block !important;
    margin-bottom: 3rem;
    padding: 1.5rem 0;
  }
  
  /* Vertical layout for work content between 300px-500px */
  .work-content-grid {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: left; /* Changed to left alignment */
    gap: 1.5rem;
    max-width: 400px;
    margin: 0 auto;
    padding: 0 1rem;
  }
  
  /* Image first with popup animation */
  .work-content-right {
    order: 1;
    width: 100%;
    animation: popupSlideUp 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  
  /* Content below image */
  .work-content-left {
    order: 2;
    width: 100%;
    gap: 1rem;
    text-align: left; /* Left alignment for content */
    align-items: flex-start; /* Align items to the left */
  }
  
  /* Title with popup animation and custom font */
  .project-title {
    font-size: 2.2rem;
    margin-bottom: 0.8rem;
    animation: popupSlideUp 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) 0.2s forwards;
    opacity: 0;
    transform: translateY(20px) scale(0.95);
    text-align: left !important; /* Force left alignment */
    font-family: url('assets/fonts/fa-regular-400.woff2') format('woff2'), sans-serif;
  }
  
  /* Credits with staggered popup animation and custom font */
  .project-credits {
    animation: popupSlideUp 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) 0.4s forwards;
    opacity: 0;
    transform: translateY(15px) scale(0.98);
    text-align: left !important; /* Force left alignment */
    align-items: flex-start !important;
  }
  
  .project-credits li {
    font-size: 1rem;
    text-align: left !important; /* Force left alignment */
    font-family: url('assets/fonts/fa-regular-400.woff2') format('woff2'), sans-serif;
  }
  
  /* Button with popup animation and custom font */
  .project-btn-wrap {
    margin-top: 1.2rem;
    animation: popupSlideUp 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) 0.6s forwards;
    opacity: 0;
    transform: translateY(10px) scale(0.99);
    text-align: left !important; /* Force left alignment */
    align-self: flex-start; /* Align button to the left */
  }
  
  .project-btn {
    font-size: 1rem;
    padding: 0.8rem 1.5rem;
    font-family: url('assets/fonts/fa-regular-400.woff2') format('woff2'), sans-serif;
  }
  
  /* Image container adjustments */
  .project-image-container {
    max-width: 350px;
    width: 100%;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
  }
  
  /* Staggered animations for different projects */
  #project-2 .work-content-right {
    animation-delay: 0.3s;
  }
  
  #project-2 .project-title {
    animation-delay: 0.5s;
  }
  
  #project-2 .project-credits {
    animation-delay: 0.7s;
  }
  
  #project-2 .project-btn-wrap {
    animation-delay: 0.9s;
  }
  
  /* Section title adjustments */
  .work-section-title {
    position: relative !important;
    top: auto !important;
    left: auto !important;
    margin-bottom: 2rem;
    text-align: center;
  }
  
  .work-section-title .title-big h2 {
    font-size: 2.2rem;
    animation: popupSlideUp 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
    opacity: 0;
    transform: translateY(20px) scale(0.95);
    font-family: url('assets/fonts/fa-regular-400.woff2') format('woff2'), sans-serif;
  }
}

@media (max-width: 480px) {
  .project-title {
    font-size: 2rem;
  }
  
  .work-section-title .title-big h2 {
    font-size: 2rem;
  }
  
  .work-content-grid {
    padding: 0 1rem;
  }
  
  .work-title-line {
    width: 60px;
    height: 2px;
  }
  
  .work-transition-section {
    height: 120vh;
  }
}

/* Blog Horizontal Scroll Styles */
.blog-carousel-section {
  height: 400vh; /* Increased height for much more scroll area */
  background-color: #000;
  position: relative;
  overflow: hidden;
}

.blog-sticky-container {
  position: sticky;
  top: 0;
  height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
  background-color: #000;
}

.blog-section-title {
  position: absolute;
  top: 10%;
  left: 2rem;
  z-index: 10;
  pointer-events: none;
}

.blog-section-title .title-big h2 {
  color: #fff;
  font-size: 4rem;
  text-align: left;
  margin-bottom: 3rem;
}

.blog-cards-wrapper {
  display: flex;
  gap: 2rem;
  will-change: transform;
  padding: 0 2rem 0 2rem;
  align-items: center;
  height: 100%;
  padding-top: 140px;
  padding-right: 50vw; /* Add extra padding to ensure last card is fully visible */
}

.blog-card {
  height: 450px;
  width: 400px;
  background-color: #1a1a1a;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #333;
}

.blog-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(255, 255, 255, 0.1);
}

.blog-card-image {
  height: 250px;
  width: 100%;
  overflow: hidden;
  position: relative;
}

.blog-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.blog-card:hover .blog-card-image img {
  transform: scale(1.1);
}

.blog-card-content {
  padding: 1.5rem;
  color: white;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.blog-card-content h4 {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: #fff;
  line-height: 1.4;
}

.blog-card-content p {
  font-size: 0.9rem;
  color: #ccc;
  line-height: 1.5;
  margin-bottom: 1rem;
  flex-grow: 1;
}

.blog-card-content .post-date {
  font-size: 0.8rem;
  color: #888;
  font-weight: 500;
  margin-top: auto;
}

/* Footer spacing fix */
.footer-section {
  position: relative;
  z-index: 100;
  background-color: #000;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .blog-card {
    width: 320px;
    height: 400px;
  }
  
  .blog-card-image {
    height: 200px;
  }
  
  .blog-card-content {
    height: 200px;
    padding: 1rem;
  }
  
  .blog-cards-wrapper {
    padding: 0 1rem;
    gap: 1.5rem;
  }
  
  .blog-carousel-section {
    height: 600vh; /* Significantly increased height for mobile - from 300vh to 600vh */
  }
  
  .blog-section-title .title-big h2 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
  }
  
  .blog-section-title {
    top: 8%;
  }
}

@media (max-width: 480px) {
  .blog-card {
    width: 280px;
    height: 380px;
  }
  
  .blog-card-image {
    height: 180px;
  }
  
  .blog-cards-wrapper {
    gap: 1rem;
  }
  
  .blog-carousel-section {
    height: 900vh; /* Increased scroll area for very small screens - from 800vh to 900vh */
  }
  
  .blog-section-title .title-big h2 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
  }
  
  .blog-section-title {
    top: 6%;
  }
}

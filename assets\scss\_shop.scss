/*======= Shop Page =======*/
.product-loop {
    .single-product {
        text-align: center;
        margin-bottom: 40px;

        .product-img {
            margin-bottom: 30px;
            position: relative;
        }

        .product-action {
            position: absolute;
            z-index: 1;
            width: 100%;
            top: 50%;
            transform: translateY(-40%);
            text-align: center;
            visibility: hidden;
            opacity: 0;
            @include transition(0.3s);

            a {
                height: 40px;
                width: 40px;
                background-color: $primary-color;
                color: $white;
                font-size: 15px;
                line-height: 40px;
                margin: 0 5px;

                &:hover {
                    background-color: $secondary-color;
                }
            }
        }

        &:hover {
            .product-action {
                visibility: visible;
                opacity: 1;
                transform: translateY(-50%);
            }
        }

        .rating {
            margin-bottom: 10px;

            li {
                display: inline-block;
                font-size: 15px;
                color: #ffb503;
            }
        }

        .title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .price {
            font-size: 15px;
            color: $primary-color;
            font-weight: 700;
        }
    }
}

/*======= Shop Sidebar =======*/
.shop-top-bar {
    margin-bottom: 40px;

    .product-search {
        position: relative;

        @media #{$tiny} {
            margin-bottom: 20px;
        }

        input {
            height: 60px;
            padding-left: 25px;
            padding-right: 50px;
            border: 2px solid #eaedf4;
            font-size: 16px;
        }

        button {
            position: absolute;
            right: 0;
            top: 0;
            height: 100%;
            background: none;
            font-size: 16px;
            color: $primary-color;
            width: 50px;
            text-align: center;
        }
    }

    .product-shorting {
        select {
            border: 2px solid #eaedf4;
            height: 60px;
            padding: 0 20px;
            color: $secondary-color;
            text-align: center;
            font-size: 16px;

            &:focus {
                outline: none;
            }
        }
    }
}

.shop-sidebar {
    @media #{$md} {
        margin-top: 50px;
    }

    .widget {
        padding: 40px 30px;
        border: 2px solid #eaedf4;
        margin-bottom: 40px;

        .widget-title {
            padding-bottom: 15px;
            margin-bottom: 30px;
            position: relative;
            font-size: 22px;

            &::before,
            &::after {
                content: "";
                position: absolute;
                left: 0;
                bottom: 0;
                height: 2px;
                width: 10px;
                background-color: $primary-color;
            }

            &::after {
                width: 25px;
                left: 18px;
            }
        }

        &.product-cat-widget {
            ul li {
                line-height: 35px;
                font-size: 16px;

                a {
                    color: $text-color;
                    display: block;

                    &:hover {
                        color: $primary-color;
                        padding-left: 10px;
                    }
                }
            }
        }

        &.product-tag-widget {
            .tags-loop {
                a {
                    background-color: #EEF3F9;
                    font-size: 14px;
                    border-radius: 5px;
                    padding: 10px 15px;
                    margin-right: 8px;
                    color: $text-color;
                    margin-bottom: 10px;

                    &:hover {
                        background-color: $primary-color;
                        color: $white;
                    }
                }
            }
        }

        &.product-filter-widget {
            .slider-range .ui-slider.ui-slider-horizontal.ui-widget.ui-widget-content.ui-corner-all {
                height: 4px;
                border: none;
                border-radius: 0;
                background-color: #DFDFDF;
            }

            .slider-range .ui-widget-header {
                background-color: $primary-color;
                border-radius: 0;
            }

            .ui-slider-handle {
                height: 10px;
                width: 10px;
                border: none;
                border-radius: 0;
                top: -3px;
                background: $primary-color;
                margin-left: -5px;

                &:focus {
                    outline: none;
                }
            }

            .price-ammount {
                position: relative;
                margin-top: 30px;

                span {
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-45%);
                    font-family: $ssp;
                    font-size: 14px;
                    font-weight: 600;
                    color: $text-color;
                }

                input {
                    color: $secondary-color;
                    font-size: 14px;
                    font-weight: 700;
                    font-family: $ssp;
                    height: auto;
                    padding-right: 0;
                    padding-left: 45px;
                }
            }
        }
    }
}

/*======= Product Details =======*/
.shop-details-wrap {
    .product-details {
        font-size: 15px;

        .product-gallery {
            @media #{$md} {
                margin-bottom: 50px;
            }

            .product-gallery-arrow {
                max-width: 150px;
                float: left;

                @media #{$lg} {
                    max-width: 120px;
                }

                @media #{$sm} {
                    max-width: 100px;
                }

                @media #{$xsm} {
                    max-width: 75px;
                }

                li {
                    margin-bottom: 20px;
                    cursor: pointer;

                    @media #{$xsm} {
                        margin-bottom: 10px;
                    }
                }
            }

            .gallery-slider-warp {
                width: calc(100% - 180px);
                float: right;

                @media #{$lg} {
                    width: calc(100% - 150px);
                }

                @media #{$sm} {
                    width: calc(100% - 120px);
                }

                @media #{$xsm} {
                    width: calc(100% - 90px);
                }
            }

            .product-gallery-slider {
                position: relative;

                .slick-arrow {
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    z-index: 2;
                    color: $white;
                    background-color: $primary-color;
                    height: 50px;
                    width: 50px;
                    line-height: 50px;
                    @include transition(0.3s);
                    opacity: 0;
                    visibility: hidden;

                    @media #{$xsm} {
                        width: 40px;
                        height: 40px;
                        line-height: 40px;
                    }

                    &.next-arrow {
                        left: auto;
                        right: 0;
                    }

                    &:hover {
                        background-color: $secondary-color;
                    }
                }
            }

            &:hover {
                .slick-arrow {
                    opacity: 1;
                    visibility: visible;
                }
            }
        }

        .product-summary {
            padding-left: 30px;

            @media #{$lg} {
                padding-left: 0;
            }

            .product-name {
                font-size: 35px;
                font-weight: 700;
                margin-bottom: 15px;
            }

            .rating {
                margin-bottom: 20px;
                line-height: 1;

                li {
                    display: inline-block;
                    font-size: 12px;
                    color: $primary-color;
                }
            }

            .price {
                font-weight: 700;
                color: $secondary-color;
                margin-bottom: 30px;
            }

            .short-description p {
                margin-bottom: 20px;
            }

            .add-to-cart-form {
                margin-top: 10px;

                form {
                    width: 100%;
                    display: flex;
                    align-items: center;

                    .quantity-input {
                        display: flex;
                        margin-right: 30px;

                        @media #{$xsm} {
                            margin-right: 20px;
                        }

                        .quantity-down,
                        .quantity-up,
                        input {
                            padding: 0;
                            height: 33px;
                            width: 45px;
                            border: 1px solid #dddddd;
                            text-align: center;
                            cursor: pointer;
                            line-height: 33px;
                            font-size: 14px;
                            color: $secondary-color;

                        }
                    }

                    button {
                        padding: 8px 30px;
                        background-color: $primary-color;
                        border: none;
                        font-weight: 700;
                        color: $white;
                        font-size: 14px;
                        font-family: $ssp;
                        @include transition(0.3s);

                        @media #{$lg} {
                            padding: 8px 25px;
                        }

                        &:hover {
                            background-color: $secondary-color;
                        }
                    }
                }
            }

            .product-share {
                margin-top: 30px;

                li {
                    display: inline-block;

                    a {
                        color: $text-color;
                        padding: 5px;

                        &:hover {
                            color: $primary-color;
                        }
                    }

                    &.title {
                        color: $secondary-color;
                        font-weight: 700;
                        margin-right: 20px;
                    }
                }
            }
        }

        .product-details-tab {
            margin-top: 70px;

            .tab-filter-nav {
                display: flex;
                margin-bottom: 40px;

                .nav {
                    border-bottom: 2px solid #dddddd;
                    padding-bottom: 10px;

                    @media #{$tiny} {
                        padding-bottom: 0;
                        border: none;
                    }

                    a {
                        font-size: 24px;
                        font-weight: 700;
                        font-family: $ssp;
                        color: $secondary-color;
                        padding: 0 10px;
                        margin-left: 20px;
                        position: relative;

                        @media #{$md} {
                            font-size: 20px;
                        }

                        @media #{$sm} {
                            margin-left: 10px;
                        }

                        @media #{$xsm} {
                            margin-left: 0;
                            padding: 0 5px;
                            font-size: 16px;
                        }

                        @media #{$tiny} {
                            margin-bottom: 0;
                            margin-right: 10px;
                        }

                        &:first-child {
                            margin-left: 0;
                            padding-left: 0;
                        }

                        &::before {
                            position: absolute;
                            left: 0;
                            bottom: -12px;
                            width: 100%;
                            height: 2px;
                            content: "";
                            background: $primary-color;
                            visibility: hidden;
                            opacity: 0;
                            @include transition(0.3s);

                            @media #{$tiny} {
                                bottom: 0;
                            }

                        }

                        &.active,
                        &:hover {
                            color: $primary-color;

                            &::before {
                                opacity: 1;
                                visibility: visible;
                            }
                        }
                    }
                }
            }

            .product-description {
                p:not(:last-child) {
                    margin-bottom: 30px;
                }
            }

            .additional-info {
                th {
                    color: $secondary-color;
                    font-weight: 600;
                    padding-right: 20px;
                }
            }

            .product-review {
                .review-list {
                    li {
                        position: relative;
                        padding-left: 100px;
                        margin-top: 40px;

                        @media #{$xsm} {
                            padding-left: 75px;
                        }

                        &:first-child {
                            margin-top: 0;
                        }

                        .review-thumb {
                            position: absolute;
                            left: 0;
                            top: 0;
                        }

                        .review-rating {
                            li {
                                display: inline-block;
                                font-size: 12px;
                                color: $primary-color;
                                padding: 0;
                                margin: 0;
                            }
                        }

                        .children {
                            &>li:first-child {
                                margin-top: 40px;
                            }

                            @media #{$xsm} {
                                margin-left: -50px;
                            }
                        }
                    }
                }
            }
        }

    }

    .related-product {
        padding-bottom: 80px;

        .related-title {
            text-align: center;
            margin-bottom: 40px;
            font-weight: 700;
            font-size: 55px;

            @media #{$md} {
                font-size: 42px;
            }

            @media #{$sm} {
                margin-left: 32px;
            }
        }
    }
}